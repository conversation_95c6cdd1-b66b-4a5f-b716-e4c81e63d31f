import admin from "firebase-admin"
import dotenv from "dotenv"

// Load environment variables
dotenv.config({ path: ".env.local" })

// For production, we'll need to use a different approach
// Let's try to use the service account from env first, then fall back to application default
let credential
try {
  if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PROD) {
    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PROD)
    credential = admin.credential.cert(serviceAccount)
  } else {
    // Fall back to application default (Firebase CLI auth)
    credential = admin.credential.applicationDefault()
  }
} catch (error) {
  console.log("Using application default credentials...")
  credential = admin.credential.applicationDefault()
}

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential,
    projectId: "brotrip-d7c58",
  })
}

const db = admin.firestore()

async function checkBookingDiscrepancies() {
  console.log("🔍 Checking for booking discrepancies...")

  try {
    // Get all bookings using collection group query
    const bookingsSnapshot = await db.collectionGroup("bookings").get()

    console.log(`📊 Found ${bookingsSnapshot.size} total bookings`)

    const discrepancies = []
    const completedBookings = []
    const confirmedBookings = []

    for (const bookingDoc of bookingsSnapshot.docs) {
      const bookingData = bookingDoc.data()
      const bookingId = bookingDoc.id

      // Extract experience ID from the document path
      const pathParts = bookingDoc.ref.path.split("/")
      const experienceId = pathParts[1] // localExperiences/{experienceId}/bookings/{bookingId}

      const booking = {
        bookingId,
        experienceId,
        status: bookingData.status,
        paymentStatus: bookingData.paymentStatus,
        date: bookingData.date,
        time: bookingData.time,
        userEmail: bookingData.userEmail,
        userName: bookingData.userName,
        experienceTitle: bookingData.experienceTitle,
        completedAt: bookingData.completedAt,
        bookedAt: bookingData.bookedAt,
        path: bookingDoc.ref.path,
      }

      if (booking.status === "completed") {
        completedBookings.push(booking)
      } else if (booking.status === "confirmed" && booking.paymentStatus === "paid") {
        confirmedBookings.push(booking)

        // Check if this booking should be completed based on date/time
        const experienceDate = booking.date // YYYY-MM-DD format
        const experienceTime = booking.time // HH:MM format

        if (experienceDate && experienceTime) {
          const experienceDateTime = new Date(`${experienceDate}T${experienceTime}:00.000Z`)
          const currentTime = new Date()

          // Assume 2 hours duration for now (we can get actual duration later)
          const experienceEndTime = new Date(experienceDateTime.getTime() + 2 * 60 * 60 * 1000)

          if (experienceEndTime <= currentTime) {
            discrepancies.push({
              ...booking,
              issue: "Should be completed but still confirmed",
              experienceEndTime: experienceEndTime.toISOString(),
              currentTime: currentTime.toISOString(),
            })
          }
        }
      }
    }

    console.log("\n📈 Summary:")
    console.log(`Total bookings: ${bookingsSnapshot.size}`)
    console.log(`Completed bookings: ${completedBookings.length}`)
    console.log(`Confirmed bookings: ${confirmedBookings.length}`)
    console.log(`Discrepancies found: ${discrepancies.length}`)

    // Show detailed breakdown of all bookings
    console.log("\n📋 Detailed Booking Breakdown:")
    const statusCounts = {}
    const allBookings = []

    for (const bookingDoc of bookingsSnapshot.docs) {
      const bookingData = bookingDoc.data()
      const bookingId = bookingDoc.id
      const pathParts = bookingDoc.ref.path.split("/")
      const experienceId = pathParts[1]

      const booking = {
        bookingId,
        experienceId,
        status: bookingData.status,
        paymentStatus: bookingData.paymentStatus,
        date: bookingData.date,
        time: bookingData.time,
        userEmail: bookingData.userEmail,
        userName: bookingData.userName,
        experienceTitle: bookingData.experienceTitle,
        completedAt: bookingData.completedAt,
        bookedAt: bookingData.bookedAt,
        path: bookingDoc.ref.path,
      }

      allBookings.push(booking)
      statusCounts[booking.status] = (statusCounts[booking.status] || 0) + 1
    }

    console.log("Status breakdown:")
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`)
    })

    // Show recent completed bookings
    const recentCompleted = completedBookings
      .filter((b) => b.completedAt)
      .sort((a, b) => {
        const aTime = a.completedAt?.toDate?.() || new Date(a.completedAt)
        const bTime = b.completedAt?.toDate?.() || new Date(b.completedAt)
        return bTime - aTime
      })
      .slice(0, 5)

    if (recentCompleted.length > 0) {
      console.log("\n🕒 Recent completed bookings:")
      recentCompleted.forEach((booking, index) => {
        const completedTime = booking.completedAt?.toDate?.() || new Date(booking.completedAt)
        console.log(
          `${index + 1}. ${booking.experienceTitle} - ${booking.userName} (${completedTime.toISOString()})`
        )
      })
    }

    if (discrepancies.length > 0) {
      console.log("\n⚠️  Discrepancies found:")
      discrepancies.forEach((discrepancy, index) => {
        console.log(`\n${index + 1}. Booking ID: ${discrepancy.bookingId}`)
        console.log(`   Experience: ${discrepancy.experienceTitle}`)
        console.log(`   User: ${discrepancy.userName} (${discrepancy.userEmail})`)
        console.log(`   Date/Time: ${discrepancy.date} at ${discrepancy.time}`)
        console.log(`   Status: ${discrepancy.status} (should be completed)`)
        console.log(`   Experience ended: ${discrepancy.experienceEndTime}`)
        console.log(`   Path: ${discrepancy.path}`)
      })
    }

    // Check for feedback documents
    console.log("\n🔍 Checking feedback documents...")
    const feedbackSnapshot = await db.collectionGroup("feedback").get()
    console.log(`📊 Found ${feedbackSnapshot.size} feedback documents`)

    const feedbackBookingIds = new Set()
    feedbackSnapshot.docs.forEach((doc) => {
      const feedbackData = doc.data()
      if (feedbackData.bookingId) {
        feedbackBookingIds.add(feedbackData.bookingId)
      }
    })

    console.log(`📊 Unique booking IDs with feedback: ${feedbackBookingIds.size}`)

    // Check for bookings that have feedback but aren't completed
    const feedbackDiscrepancies = []
    for (const bookingDoc of bookingsSnapshot.docs) {
      const bookingData = bookingDoc.data()
      const bookingId = bookingDoc.id

      if (feedbackBookingIds.has(bookingId) && bookingData.status !== "completed") {
        const pathParts = bookingDoc.ref.path.split("/")
        const experienceId = pathParts[1]

        feedbackDiscrepancies.push({
          bookingId,
          experienceId,
          status: bookingData.status,
          experienceTitle: bookingData.experienceTitle,
          userName: bookingData.userName,
          userEmail: bookingData.userEmail,
          date: bookingData.date,
          time: bookingData.time,
          issue: "Has feedback but not completed status",
        })
      }
    }

    if (feedbackDiscrepancies.length > 0) {
      console.log("\n⚠️  Feedback discrepancies found:")
      feedbackDiscrepancies.forEach((discrepancy, index) => {
        console.log(`\n${index + 1}. Booking ID: ${discrepancy.bookingId}`)
        console.log(`   Experience: ${discrepancy.experienceTitle}`)
        console.log(`   User: ${discrepancy.userName} (${discrepancy.userEmail})`)
        console.log(`   Date/Time: ${discrepancy.date} at ${discrepancy.time}`)
        console.log(`   Status: ${discrepancy.status} (has feedback but not completed)`)
      })
    }

    // Check if completed bookings should have triggered feedback emails
    console.log("\n📧 Checking completed bookings for feedback email triggers...")
    const completedBookingsAnalysis = []

    for (const booking of completedBookings) {
      // Check if this booking should have triggered a feedback email
      const shouldHaveFeedbackEmail = booking.status === "completed" && booking.completedAt

      if (shouldHaveFeedbackEmail) {
        const completedTime = booking.completedAt?.toDate?.() || new Date(booking.completedAt)
        const timeSinceCompletion = Date.now() - completedTime.getTime()
        const hoursSinceCompletion = timeSinceCompletion / (1000 * 60 * 60)

        completedBookingsAnalysis.push({
          ...booking,
          completedTime,
          hoursSinceCompletion,
          shouldHaveFeedbackEmail: true,
          hasFeedbackDocument: feedbackBookingIds.has(booking.bookingId),
        })
      }
    }

    console.log(
      `Found ${completedBookingsAnalysis.length} completed bookings that should have feedback emails`
    )

    const missingFeedbackEmails = completedBookingsAnalysis.filter((b) => !b.hasFeedbackDocument)

    if (missingFeedbackEmails.length > 0) {
      console.log(
        `\n⚠️  ${missingFeedbackEmails.length} completed bookings missing feedback documents:`
      )
      missingFeedbackEmails.forEach((booking, index) => {
        console.log(`\n${index + 1}. Booking ID: ${booking.bookingId}`)
        console.log(`   Experience: ${booking.experienceTitle}`)
        console.log(`   User: ${booking.userName} (${booking.userEmail})`)
        console.log(`   Completed: ${booking.completedTime.toISOString()}`)
        console.log(`   Hours since completion: ${booking.hoursSinceCompletion.toFixed(1)}`)
        console.log(`   Path: ${booking.path}`)
      })

      console.log(
        "\n💡 These bookings should have triggered feedback emails when they were marked as completed."
      )
      console.log(
        "   This suggests the Firebase function onBookingUpdated may not have fired properly."
      )
    } else {
      console.log("✅ All completed bookings either have feedback documents or are too recent.")
    }

    return {
      totalBookings: bookingsSnapshot.size,
      completedBookings: completedBookings.length,
      confirmedBookings: confirmedBookings.length,
      discrepancies,
      feedbackDiscrepancies,
      feedbackDocuments: feedbackSnapshot.size,
    }
  } catch (error) {
    console.error("Error checking discrepancies:", error)
    throw error
  }
}

// Run the check
checkBookingDiscrepancies()
  .then((result) => {
    console.log("\n✅ Check completed successfully")
    process.exit(0)
  })
  .catch((error) => {
    console.error("❌ Check failed:", error)
    process.exit(1)
  })
