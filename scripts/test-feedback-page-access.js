import admin from "firebase-admin"
import dotenv from "dotenv"

// Load environment variables
dotenv.config({ path: ".env.local" })

const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '{}')

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: "brotrip-mvp"
  })
}

const db = admin.firestore()

async function testFeedbackPageAccess() {
  console.log('🧪 Testing feedback page access for migrated bookings...')
  
  const testUserId = 'U8TeGBYI2WV4DuBgXF1Ep24eZQF2'
  const testBookingIds = [
    'migrated-cUI5DzgaqozEPJ5bJfMw',
    'migrated-DUuBMHvvfdxMbObTm9yg'
  ]
  
  try {
    for (const bookingId of testBookingIds) {
      console.log(`\n🔍 Testing booking: ${bookingId}`)
      
      // Simulate the logic from UserLocalExperienceBookingsService.getUserBooking
      const bookingRef = db
        .collection('users')
        .doc(testUserId)
        .collection('localExperienceBookings')
        .doc(bookingId)
      
      const bookingDoc = await bookingRef.get()
      
      if (!bookingDoc.exists) {
        console.log('❌ Booking not found')
        continue
      }
      
      const bookingData = {
        id: bookingDoc.id,
        ...bookingDoc.data()
      }
      
      console.log(`📋 Booking found:`)
      console.log(`   ID: ${bookingData.id}`)
      console.log(`   User ID: ${bookingData.userId}`)
      console.log(`   Status: ${bookingData.status}`)
      console.log(`   Experience: ${bookingData.experienceTitle}`)
      console.log(`   Date/Time: ${bookingData.date} at ${bookingData.time}`)
      
      // Test the validation logic from the feedback page
      console.log(`\n🔒 Testing feedback page validation:`)
      
      // 1. Check if booking belongs to user
      if (bookingData.userId !== testUserId) {
        console.log('❌ User mismatch - would redirect')
        continue
      } else {
        console.log('✅ User ownership verified')
      }
      
      // 2. Check if booking is completed
      if (bookingData.status !== 'completed') {
        console.log(`❌ Booking not completed (status: ${bookingData.status}) - would redirect`)
        continue
      } else {
        console.log('✅ Booking status is completed')
      }
      
      // 3. Check if feedback already exists
      const feedbackRef = db
        .collection('localExperiences')
        .doc(bookingData.experienceId)
        .collection('feedback')
        .doc(bookingId)
      
      const feedbackDoc = await feedbackRef.get()
      
      if (feedbackDoc.exists) {
        const feedbackData = feedbackDoc.data()
        if (feedbackData.userFeedback) {
          console.log('⚠️  User feedback already submitted')
        } else {
          console.log('✅ No user feedback found - can submit')
        }
      } else {
        console.log('✅ No feedback document found - can submit')
      }
      
      console.log(`\n🎯 Result: Feedback page should be ACCESSIBLE for ${bookingId}`)
      console.log(`   URL: http://localhost:3000/experiences/feedback/${bookingId}`)
    }
    
    console.log('\n✅ Feedback page access test completed')
    
  } catch (error) {
    console.error('❌ Error testing feedback page access:', error)
    throw error
  }
}

// Run the test
testFeedbackPageAccess()
  .then(() => {
    console.log('\n🎉 Test completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Test failed:', error)
    process.exit(1)
  })
