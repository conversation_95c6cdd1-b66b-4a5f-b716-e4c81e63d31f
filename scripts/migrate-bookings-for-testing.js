import admin from "firebase-admin"
import dotenv from "dotenv"

// Load environment variables
dotenv.config({ path: ".env.local" })

// For production, we'll need to use a different approach
let credential
try {
  if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PROD) {
    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PROD)
    credential = admin.credential.cert(serviceAccount)
  } else {
    // Fall back to application default (Firebase CLI auth)
    credential = admin.credential.applicationDefault()
  }
} catch (error) {
  console.log("Using application default credentials...")
  credential = admin.credential.applicationDefault()
}

// Initialize Firebase Admin SDK for production
const prodApp = admin.initializeApp({
  credential,
  projectId: "brotrip-d7c58",
}, 'prod')

// Initialize Firebase Admin SDK for local/dev
const localServiceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '{}')
const localApp = admin.initializeApp({
  credential: admin.credential.cert(localServiceAccount),
  projectId: "brotrip-mvp",
}, 'local')

const prodDb = prodApp.firestore()
const localDb = localApp.firestore()

async function getSpecificBookingDetails(bookingIds) {
  console.log('🔍 Getting specific booking details from production...')
  
  try {
    const bookingsSnapshot = await prodDb.collectionGroup('bookings').get()
    const specificBookings = []
    
    for (const bookingDoc of bookingsSnapshot.docs) {
      const bookingId = bookingDoc.id
      
      if (bookingIds.includes(bookingId)) {
        const bookingData = bookingDoc.data()
        const pathParts = bookingDoc.ref.path.split('/')
        const experienceId = pathParts[1]
        
        // Get the full booking data
        const booking = {
          bookingId,
          experienceId,
          ...bookingData,
          path: bookingDoc.ref.path
        }
        
        specificBookings.push(booking)
        
        console.log(`\n📋 Found booking ${bookingId}:`)
        console.log(`   Experience ID: ${experienceId}`)
        console.log(`   Experience Title: ${bookingData.experienceTitle}`)
        console.log(`   Status: ${bookingData.status}`)
        console.log(`   Payment Status: ${bookingData.paymentStatus}`)
        console.log(`   User: ${bookingData.userName} (${bookingData.userEmail})`)
        console.log(`   Date/Time: ${bookingData.date} at ${bookingData.time}`)
        console.log(`   Completed At: ${bookingData.completedAt?.toDate?.()?.toISOString() || bookingData.completedAt}`)
        console.log(`   Path: ${bookingDoc.ref.path}`)
      }
    }
    
    return specificBookings
  } catch (error) {
    console.error('Error getting specific bookings:', error)
    throw error
  }
}

async function getLocalExperiences() {
  console.log('🔍 Getting local experiences from dev database...')
  
  try {
    const experiencesSnapshot = await localDb.collection('localExperiences').get()
    const experiences = []
    
    experiencesSnapshot.docs.forEach(doc => {
      const data = doc.data()
      experiences.push({
        id: doc.id,
        title: data.title,
        shortDescription: data.shortDescription
      })
    })
    
    console.log(`📊 Found ${experiences.length} local experiences:`)
    experiences.forEach((exp, index) => {
      console.log(`${index + 1}. ${exp.id} - ${exp.title}`)
    })
    
    return experiences
  } catch (error) {
    console.error('Error getting local experiences:', error)
    throw error
  }
}

async function migrateBookingsToLocal(prodBookings, targetUserId = 'U8TeGBYI2WV4DuBgXF1Ep24eZQF2', targetUserEmail = '<EMAIL>') {
  console.log('\n🚀 Migrating bookings to local database...')
  
  try {
    // Get local experiences to map to
    const localExperiences = await getLocalExperiences()
    
    // Create a simple mapping based on title similarity
    const experienceMapping = {}
    
    for (const prodBooking of prodBookings) {
      const prodTitle = prodBooking.experienceTitle.toLowerCase()
      
      // Find matching local experience
      const matchingLocal = localExperiences.find(local => {
        const localTitle = local.title.toLowerCase()
        return localTitle.includes(prodTitle.split(' ')[0]) || prodTitle.includes(localTitle.split(' ')[0])
      })
      
      if (matchingLocal) {
        experienceMapping[prodBooking.experienceId] = matchingLocal.id
        console.log(`📍 Mapping: ${prodBooking.experienceTitle} -> ${matchingLocal.title} (${matchingLocal.id})`)
      } else {
        // Use the first available experience as fallback
        experienceMapping[prodBooking.experienceId] = localExperiences[0].id
        console.log(`⚠️  No match found for ${prodBooking.experienceTitle}, using fallback: ${localExperiences[0].title}`)
      }
    }
    
    // Migrate each booking
    for (const prodBooking of prodBookings) {
      const localExperienceId = experienceMapping[prodBooking.experienceId]
      const newBookingId = `migrated-${prodBooking.bookingId}`
      
      // Prepare the booking data for local environment
      const localBookingData = {
        ...prodBooking,
        id: newBookingId,
        userId: targetUserId,
        userEmail: targetUserEmail,
        userName: 'Test User (Migrated)',
        experienceId: localExperienceId,
        // Keep original timestamps but ensure they're Firestore timestamps
        bookedAt: prodBooking.bookedAt,
        completedAt: prodBooking.completedAt,
        confirmedAt: prodBooking.confirmedAt,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      }
      
      // Remove the path field as it's not part of the data
      delete localBookingData.path
      delete localBookingData.bookingId
      
      console.log(`\n📝 Creating booking ${newBookingId}:`)
      console.log(`   Local Experience ID: ${localExperienceId}`)
      console.log(`   User: ${targetUserEmail}`)
      console.log(`   Status: ${localBookingData.status}`)
      
      // Create in experience bookings collection
      const expBookingRef = localDb
        .collection('localExperiences')
        .doc(localExperienceId)
        .collection('bookings')
        .doc(newBookingId)
      
      await expBookingRef.set(localBookingData)
      
      // Create in user bookings collection
      const userBookingRef = localDb
        .collection('users')
        .doc(targetUserId)
        .collection('localExperienceBookings')
        .doc(newBookingId)
      
      await userBookingRef.set(localBookingData)
      
      console.log(`✅ Created booking in both collections`)
    }
    
    console.log(`\n🎉 Successfully migrated ${prodBookings.length} bookings to local database`)
    console.log(`📧 User email: ${targetUserEmail}`)
    console.log(`🆔 User ID: ${targetUserId}`)
    
    return prodBookings.length
  } catch (error) {
    console.error('Error migrating bookings:', error)
    throw error
  }
}

// Main execution
const specificBookingIds = process.argv.slice(2)

if (specificBookingIds.length === 0) {
  console.error('❌ Please provide booking IDs as arguments')
  console.log('Usage: node migrate-bookings-for-testing.js DUuBMHvvfdxMbObTm9yg cUI5DzgaqozEPJ5bJfMw')
  process.exit(1)
}

console.log(`🎯 Getting details for specific bookings: ${specificBookingIds.join(', ')}`)

getSpecificBookingDetails(specificBookingIds)
  .then(async (bookings) => {
    console.log(`\n✅ Found ${bookings.length} specific bookings`)
    
    if (bookings.length > 0) {
      await migrateBookingsToLocal(bookings)
    }
    
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Failed to get and migrate bookings:', error)
    process.exit(1)
  })
