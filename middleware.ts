import { NextRequest, NextResponse } from "next/server"

// Define protected API routes
const protectedApiRoutes = [
  "/api/ai",
  "/api/weather",
  "/api/places",
  "/api/images/google-places",
  "/api/email",
  "/api/invitation",
]

// Define protected page routes that need auth for SSR
const protectedPageRoutes = [
  "/dashboard",
  "/test-dashboard",
  "/trips",
  "/squads",
  "/settings",
  "/calendar",
  "/experiences",
]

const bypassRoutes = ["/api/places/autocomplete-public", "/api/invitation/public"]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if the request is for a protected API route
  const isProtectedApiRoute = protectedApiRoutes.some((route) => pathname.startsWith(route))
  const isProtectedPageRoute = protectedPageRoutes.some((route) => pathname.startsWith(route))
  const isBypassRoute = bypassRoutes.some((route) => pathname.startsWith(route))

  // Handle protected page routes (for SSR auth)
  // if (isProtectedPageRoute) {
  //   // Get token from cookies or Authorization header
  //   const cookieToken = request.cookies.get("firebase-auth-token")?.value

  //   if (!cookieToken) {
  //     // Add token to cookies for server components to access
  //     return NextResponse.json({ error: "Unauthorized: Missing or invalid token" }, { status: 401 })
  //   }

  //   // token found - let the server component handle the redirect
  //   return NextResponse.next()
  // }

  // Handle protected API routes
  if (isProtectedApiRoute && !isBypassRoute) {
    try {
      // Get the Authorization header
      const authHeader = request.headers.get("Authorization")

      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return NextResponse.json(
          { error: "Unauthorized: Missing or invalid token" },
          { status: 401 }
        )
      }

      // Extract the token and pass it to the API route
      const token = authHeader.split("Bearer ")[1]

      // Add the token to the request headers for the API route to verify
      const requestHeaders = new Headers(request.headers)
      requestHeaders.set("X-Auth-Token", token)

      // Continue to the API route with the token in headers
      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })
    } catch (error) {
      console.error("Error in middleware:", error)
      return NextResponse.json({ error: "Authentication error" }, { status: 500 })
    }
  }

  // For non-protected routes, continue normally
  return NextResponse.next()
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    // API routes
    "/api/ai/:path*",
    "/api/weather/:path*",
    "/api/places/:path*",
    "/api/images/google-places/:path*",
    "/api/email/:path*",
    "/api/invitation/:path*",
    // Protected page routes for SSR auth
    "/dashboard/:path*",
    "/test-dashboard/:path*",
    "/trips/:path*",
    "/squads/:path*",
    "/settings/:path*",
    "/calendar/:path*",
    "/experiences/:path*",
  ],
}
