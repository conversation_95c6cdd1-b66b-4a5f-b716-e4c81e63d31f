import { NextRequest, NextResponse } from "next/server"
import { getAdminDb, getAdminFieldValue } from "@/lib/firebase-admin"

/**
 * API key authentication for admin endpoints
 */
function validateApiKey(request: NextRequest): boolean {
  const authHeader = request.headers.get("Authorization")
  const expectedApiKey = process.env.ADMIN_API_KEY

  if (!expectedApiKey) {
    console.error("ADMIN_API_KEY environment variable is not set")
    return false
  }

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return false
  }

  const providedApiKey = authHeader.split("Bearer ")[1]
  return providedApiKey === expectedApiKey
}

/**
 * Create experience from approved submission
 */
async function createExperienceFromSubmission(submissionData: any): Promise<{ success: boolean; id?: string; error?: string }> {
  try {
    // Call the existing experience creation API internally
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/admin/experiences/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.LOCAL_EXPERIENCE_API_KEY}`
      },
      body: JSON.stringify(submissionData.experienceData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      return { success: false, error: errorData.error || 'Failed to create experience' }
    }

    const result = await response.json()
    return { success: true, id: result.data.id }
  } catch (error) {
    console.error("Error creating experience from submission:", error)
    return { success: false, error: "Failed to create experience" }
  }
}

/**
 * Send notification email to host
 */
async function sendHostNotificationEmail(
  hostEmail: string, 
  experienceTitle: string, 
  action: string, 
  notes?: string
): Promise<void> {
  try {
    // TODO: Implement email notification
    // This could use SendGrid, AWS SES, or your preferred email service
    console.log(`Sending ${action} notification to ${hostEmail} for "${experienceTitle}"`)
    if (notes) {
      console.log(`Notes: ${notes}`)
    }
  } catch (error) {
    console.error("Error sending notification email:", error)
  }
}

/**
 * POST /api/admin/experience-submissions/review
 * Process admin review of experience submission
 */
export async function POST(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { success: false, error: "Unauthorized: Invalid API key" },
        { status: 401 }
      )
    }

    // Parse request body
    const { submissionId, action, notes } = await request.json()

    if (!submissionId || !action) {
      return NextResponse.json(
        { success: false, error: "Missing required fields: submissionId, action" },
        { status: 400 }
      )
    }

    if (!["approve", "reject", "changes"].includes(action)) {
      return NextResponse.json(
        { success: false, error: "Invalid action. Must be: approve, reject, or changes" },
        { status: 400 }
      )
    }

    const adminDb = await getAdminDb()
    const adminFieldValue = await getAdminFieldValue()

    // Get the submission
    const submissionRef = adminDb.collection("pendingExperienceSubmissions").doc(submissionId)
    const submissionDoc = await submissionRef.get()

    if (!submissionDoc.exists) {
      return NextResponse.json(
        { success: false, error: "Submission not found" },
        { status: 404 }
      )
    }

    const submissionData = submissionDoc.data()

    // Check if already processed
    if (submissionData?.status !== "pending") {
      return NextResponse.json(
        { success: false, error: "Submission has already been processed" },
        { status: 400 }
      )
    }

    let experienceId: string | undefined
    let updateData: any = {
      status: action === "approve" ? "approved" : action === "reject" ? "rejected" : "needs_changes",
      reviewedAt: adminFieldValue.serverTimestamp(),
      reviewNotes: notes || null,
      updatedAt: adminFieldValue.serverTimestamp()
    }

    // If approving, create the experience
    if (action === "approve") {
      const createResult = await createExperienceFromSubmission(submissionData)
      
      if (!createResult.success) {
        return NextResponse.json(
          { success: false, error: `Failed to create experience: ${createResult.error}` },
          { status: 500 }
        )
      }

      experienceId = createResult.id
      updateData.experienceId = experienceId
      updateData.approvedAt = adminFieldValue.serverTimestamp()
    }

    // Update submission status
    await submissionRef.update(updateData)

    // Send notification email to host
    const hostEmail = submissionData.experienceData?.host?.email
    const experienceTitle = submissionData.experienceData?.title
    
    if (hostEmail && experienceTitle) {
      await sendHostNotificationEmail(hostEmail, experienceTitle, action, notes)
    }

    // Prepare response
    let responseMessage = ""
    switch (action) {
      case "approve":
        responseMessage = `Experience approved and created successfully${experienceId ? ` (ID: ${experienceId})` : ""}`
        break
      case "reject":
        responseMessage = "Experience submission rejected"
        break
      case "changes":
        responseMessage = "Changes requested for experience submission"
        break
    }

    return NextResponse.json({
      success: true,
      data: {
        submissionId,
        action,
        experienceId,
        message: responseMessage
      }
    })

  } catch (error) {
    console.error("Error processing experience submission review:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * GET /api/admin/experience-submissions/review
 * Get all experience submissions for admin review
 */
export async function GET(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { success: false, error: "Unauthorized: Invalid API key" },
        { status: 401 }
      )
    }

    const adminDb = await getAdminDb()
    
    // Get query parameters
    const url = new URL(request.url)
    const status = url.searchParams.get("status") // pending, approved, rejected, needs_changes
    const limit = parseInt(url.searchParams.get("limit") || "50")

    // Build query
    let query = adminDb.collection("pendingExperienceSubmissions")
      .orderBy("createdAt", "desc")
      .limit(limit)

    if (status) {
      query = query.where("status", "==", status) as any
    }

    const snapshot = await query.get()
    const submissions = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    return NextResponse.json({
      success: true,
      data: {
        submissions,
        total: submissions.length
      }
    })

  } catch (error) {
    console.error("Error fetching experience submissions:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
