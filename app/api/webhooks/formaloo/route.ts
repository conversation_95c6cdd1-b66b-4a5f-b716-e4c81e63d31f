import { NextRequest, NextResponse } from "next/server"

/**
 * Formaloo webhook endpoint for approved experience submissions
 * Only processes submissions when status is changed to "Approved"
 */

interface FormalooWebhookPayload {
  data: {
    rows: Array<{
      data: Record<string, any>
      rendered_data: Array<{
        slug: string
        title: string
        value: any
        raw_value: any
      }>
      slug: string
      submit_code: string
      created_at: string
      updated_at: string
    }>
  }
}

/**
 * Transform Formaloo data to experience creation format
 */
function transformFormalooToExperience(formalooData: Record<string, any>) {
  const errors: string[] = []
  
  // Extract basic fields
  const title = formalooData.BqeyVEvq || ""
  const description = formalooData.iiGea5NY || ""
  const basePrice = typeof formalooData.xpvaozke === 'number' ? formalooData.xpvaozke : 0
  const maxGuests = typeof formalooData.AAqrHN7E === 'number' ? formalooData.AAqrHN7E : 0
  const minGuests = parseInt(formalooData.VFJCVgcU) || 1
  
  // Extract host information
  const hostName = formalooData.A8AGLqvt || ""
  const hostEmail = formalooData.BIty7HNh || ""
  const hostPhone = formalooData.UaULIPVA || ""
  
  // Extract images
  const images: string[] = []
  if (formalooData.yD6EROaP && typeof formalooData.yD6EROaP === 'string') {
    images.push(formalooData.yD6EROaP)
  }
  if (formalooData["1WEHw2qp"] && typeof formalooData["1WEHw2qp"] === 'string') {
    images.push(formalooData["1WEHw2qp"])
  }

  // Validate required fields
  if (!title) errors.push("Experience title is required")
  if (!description) errors.push("Experience description is required")
  if (basePrice <= 0) errors.push("Valid price is required")
  if (maxGuests <= 0) errors.push("Valid max guests is required")
  if (!hostName) errors.push("Host name is required")
  if (images.length === 0) errors.push("At least one image is required")

  // Transform availability data
  const availabilityData = formalooData["1eUzcbfh"] || []
  const weeklySchedule: Record<string, any[]> = {}
  
  if (Array.isArray(availabilityData)) {
    for (const entry of availabilityData) {
      const dayName = entry.WqoyOoud?.trim()
      const startTime = entry.Wkp5pYJl
      const endTime = entry.Dyan2EKX
      
      if (!dayName || !startTime || !endTime) continue
      
      const normalizedDay = dayName.toLowerCase().replace(/\s+/g, '')
      
      const timeSlot = {
        time: startTime,
        available: true,
        maxGuests: 8
      }
      
      if (!weeklySchedule[normalizedDay]) {
        weeklySchedule[normalizedDay] = []
      }
      
      weeklySchedule[normalizedDay].push(timeSlot)
    }
  }

  // Build experience data for API
  const experienceData = {
    title,
    description,
    shortDescription: description.substring(0, 100) + "...",
    host: {
      name: hostName,
      email: hostEmail,
      phone: hostPhone,
      responseTime: "Usually responds within a few hours",
      languages: ["English"],
      bio: `Experienced host offering ${title}`,
      internalHostEmail: hostEmail
    },
    location: {
      address: "Address to be provided", // TODO: Add to form
      city: "City to be provided", // TODO: Add to form  
      country: "Country to be provided" // TODO: Add to form
    },
    pricing: {
      basePrice,
      currency: "USD"
    },
    duration: 120, // Default: 2 hours (TODO: Add to form)
    maxGuests,
    minGuests,
    categories: ["adventure"], // Default (TODO: Add to form)
    images,
    inclusions: [
      { item: "Professional guide", included: true },
      { item: "Safety equipment", included: true },
      { item: "Transportation", included: false }
    ],
    cancellationPolicy: "Free cancellation up to 24 hours before the experience",
    isActive: true, // Approved submissions go live immediately
    bookingModel: "per_max_guest",
    availability: Object.keys(weeklySchedule).length > 0 ? { weeklySchedule } : undefined
  }

  return {
    success: errors.length === 0,
    data: experienceData,
    errors
  }
}

/**
 * Validate webhook signature
 */
function validateWebhookSignature(request: NextRequest): boolean {
  const authHeader = request.headers.get("Authorization")
  const expectedApiKey = process.env.FORMALOO_WEBHOOK_SECRET
  
  if (!expectedApiKey) {
    console.error("FORMALOO_WEBHOOK_SECRET environment variable is not set")
    return false
  }
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return false
  }
  
  const providedKey = authHeader.split("Bearer ")[1]
  return providedKey === expectedApiKey
}

/**
 * Create experience using existing API
 */
async function createExperience(experienceData: any): Promise<{ success: boolean; id?: string; error?: string }> {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/admin/experiences/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.LOCAL_EXPERIENCE_API_KEY}`
      },
      body: JSON.stringify(experienceData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      return { success: false, error: errorData.error || 'Failed to create experience' }
    }

    const result = await response.json()
    return { success: true, id: result.data.id }
  } catch (error) {
    console.error("Error creating experience:", error)
    return { success: false, error: "Failed to create experience" }
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔥 FORMALOO WEBHOOK RECEIVED")
    
    // Validate webhook signature
    if (!validateWebhookSignature(request)) {
      console.error("❌ Invalid webhook signature")
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Parse webhook payload
    const payload: FormalooWebhookPayload = await request.json()
    console.log("📝 Webhook payload received:", payload.data.rows.length, "submissions")

    if (!payload.data?.rows || payload.data.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: "No submission data found" },
        { status: 400 }
      )
    }

    // Process each submission
    const results = []
    
    for (const submission of payload.data.rows) {
      const formData = submission.data
      
      // Check if this submission is approved
      const statusValue = formData.WhC0gxYm // Status field slug
      
      if (statusValue !== "AY0UjlON") { // Not approved (AY0UjlON = "A" = Approved)
        console.log(`Skipping submission ${submission.slug} - Status: ${statusValue} (not approved)`)
        continue
      }

      console.log(`Processing approved submission: ${submission.slug}`)

      // Transform the data
      const transformation = transformFormalooToExperience(formData)
      
      if (!transformation.success) {
        console.error("❌ Data transformation failed:", transformation.errors)
        results.push({
          submissionId: submission.slug,
          success: false,
          error: "Invalid form data",
          details: transformation.errors
        })
        continue
      }

      // Create the experience
      const createResult = await createExperience(transformation.data)
      
      if (createResult.success) {
        console.log("✅ Experience created:", createResult.id)
        results.push({
          submissionId: submission.slug,
          success: true,
          experienceId: createResult.id
        })
      } else {
        console.error("❌ Experience creation failed:", createResult.error)
        results.push({
          submissionId: submission.slug,
          success: false,
          error: createResult.error
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        processed: results.length,
        results
      }
    })

  } catch (error) {
    console.error("💥 Error processing Formaloo webhook:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
