import { NextRequest, NextResponse } from "next/server"
import { getAdminDb, getAdminFieldValue } from "@/lib/firebase-admin"

/**
 * Formaloo webhook endpoint for experience submissions
 * POST /api/webhooks/formaloo
 */

interface FormalooWebhookPayload {
  data: {
    rows: Array<{
      data: Record<string, any>
      rendered_data: Array<{
        slug: string
        title: string
        value: any
        raw_value: any
      }>
      slug: string
      submit_code: string
      created_at: string
      updated_at: string
    }>
  }
}

// Day slug mapping from Formaloo form
const DAY_SLUG_MAPPING = {
  "8RgSOWGK": "sunday",
  "ydersbLY": "monday", 
  "XMawvFz1": "tuesday",
  "kMqh5x4r": "wednesday",
  "PxUQpaso": "thursday",
  "DQIJlz1V": "friday",
  "aNDQM0nB": "saturday"
} as const

/**
 * Transform Formaloo data to pending experience submission format
 */
function transformFormalooData(formalooData: Record<string, any>) {
  const errors: string[] = []
  const warnings: string[] = []

  // Extract basic fields
  const title = formalooData.BqeyVEvq || ""
  const description = formalooData.iiGea5NY || ""
  const basePrice = typeof formalooData.xpvaozke === 'number' ? formalooData.xpvaozke : 0
  const maxGuests = typeof formalooData.AAqrHN7E === 'number' ? formalooData.AAqrHN7E : 0
  const minGuests = parseInt(formalooData.VFJCVgcU) || 1
  
  // Extract host information
  const hostName = formalooData.A8AGLqvt || ""
  const hostEmail = formalooData.BIty7HNh || ""
  const hostPhone = formalooData.UaULIPVA || ""
  
  // Extract images
  const images: string[] = []
  if (formalooData.yD6EROaP && typeof formalooData.yD6EROaP === 'string') {
    images.push(formalooData.yD6EROaP)
  }
  if (formalooData["1WEHw2qp"] && typeof formalooData["1WEHw2qp"] === 'string') {
    images.push(formalooData["1WEHw2qp"])
  }

  // Validate required fields
  if (!title) errors.push("Experience title is required")
  if (!description) errors.push("Experience description is required")
  if (basePrice <= 0) errors.push("Valid price is required")
  if (maxGuests <= 0) errors.push("Valid max guests is required")
  if (!hostName) errors.push("Host name is required")
  if (images.length === 0) errors.push("At least one image is required")

  // Transform availability data
  const selectedDays = formalooData.XLbI01J5 || []
  const availabilityData = formalooData["1eUzcbfh"] || []
  
  const weeklySchedule: Record<string, any[]> = {}
  
  if (Array.isArray(availabilityData)) {
    for (const entry of availabilityData) {
      const dayName = entry.WqoyOoud?.trim()
      const startTime = entry.Wkp5pYJl
      const endTime = entry.Dyan2EKX
      
      if (!dayName || !startTime || !endTime) continue
      
      const normalizedDay = dayName.toLowerCase().replace(/\s+/g, '')
      
      const timeSlot = {
        time: startTime,
        available: true,
        maxGuests: 8
      }
      
      if (!weeklySchedule[normalizedDay]) {
        weeklySchedule[normalizedDay] = []
      }
      
      weeklySchedule[normalizedDay].push(timeSlot)
    }
  }

  // Build submission data
  const submissionData = {
    // Original form data
    originalFormData: formalooData,
    
    // Transformed experience data (ready for API)
    experienceData: {
      title,
      description,
      shortDescription: description.substring(0, 100) + "...",
      host: {
        name: hostName,
        email: hostEmail,
        phone: hostPhone,
        responseTime: "Usually responds within a few hours",
        languages: ["English"],
        bio: `Experienced host offering ${title}`,
        internalHostEmail: hostEmail
      },
      location: {
        address: "Address to be provided", // TODO: Add to form
        city: "City to be provided", // TODO: Add to form
        country: "Country to be provided" // TODO: Add to form
      },
      pricing: {
        basePrice,
        currency: "USD"
      },
      duration: 120, // Default: 2 hours (TODO: Add to form)
      maxGuests,
      minGuests,
      categories: ["adventure"], // Default (TODO: Add to form)
      images,
      inclusions: [
        { item: "Professional guide", included: true },
        { item: "Safety equipment", included: true },
        { item: "Transportation", included: false }
      ],
      cancellationPolicy: "Free cancellation up to 24 hours before the experience",
      isActive: false, // Will be activated after approval
      bookingModel: "per_max_guest",
      availability: Object.keys(weeklySchedule).length > 0 ? { weeklySchedule } : undefined
    },
    
    // Submission metadata
    status: "pending", // pending, approved, rejected, needs_changes
    submittedAt: new Date(),
    validationErrors: errors,
    validationWarnings: warnings
  }

  return {
    success: errors.length === 0,
    data: submissionData,
    errors,
    warnings
  }
}

/**
 * Validate webhook signature (if Formaloo provides one)
 */
function validateWebhookSignature(request: NextRequest): boolean {
  // TODO: Implement Formaloo webhook signature validation
  // For now, we'll use a simple API key check
  const authHeader = request.headers.get("Authorization")
  const expectedApiKey = process.env.FORMALOO_WEBHOOK_SECRET
  
  if (!expectedApiKey) {
    console.error("FORMALOO_WEBHOOK_SECRET environment variable is not set")
    return false
  }
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return false
  }
  
  const providedKey = authHeader.split("Bearer ")[1]
  return providedKey === expectedApiKey
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔥 FORMALOO WEBHOOK RECEIVED")
    
    // Validate webhook signature
    if (!validateWebhookSignature(request)) {
      console.error("❌ Invalid webhook signature")
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Parse webhook payload
    const payload: FormalooWebhookPayload = await request.json()
    console.log("📝 Webhook payload received:", payload.data.rows.length, "submissions")

    if (!payload.data?.rows || payload.data.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: "No submission data found" },
        { status: 400 }
      )
    }

    // Process the first submission (assuming one submission per webhook)
    const submission = payload.data.rows[0]
    const formData = submission.data

    // Transform the data
    const transformation = transformFormalooData(formData)
    
    if (!transformation.success) {
      console.error("❌ Data transformation failed:", transformation.errors)
      return NextResponse.json(
        { success: false, error: "Invalid form data", details: transformation.errors },
        { status: 400 }
      )
    }

    // Store in pending submissions collection
    const adminDb = await getAdminDb()
    const adminFieldValue = await getAdminFieldValue()
    
    const submissionRef = adminDb.collection("pendingExperienceSubmissions").doc()
    const submissionId = submissionRef.id

    const pendingSubmission = {
      ...transformation.data,
      id: submissionId,
      formalooSubmissionId: submission.slug,
      formalooSubmitCode: submission.submit_code,
      createdAt: adminFieldValue.serverTimestamp(),
      updatedAt: adminFieldValue.serverTimestamp()
    }

    await submissionRef.set(pendingSubmission)

    console.log("✅ Pending submission created:", submissionId)

    // TODO: Send admin notification email
    // await sendAdminNotificationEmail(submissionId, transformation.data.experienceData.title)

    return NextResponse.json({
      success: true,
      data: {
        submissionId,
        status: "pending",
        message: "Experience submission received and pending approval"
      }
    })

  } catch (error) {
    console.error("💥 Error processing Formaloo webhook:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
