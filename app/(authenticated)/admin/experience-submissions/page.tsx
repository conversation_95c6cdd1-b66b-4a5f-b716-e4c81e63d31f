"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { CheckCircle, XCircle, Clock, AlertCircle, Eye } from "lucide-react"

interface PendingSubmission {
  id: string
  status: "pending" | "approved" | "rejected" | "needs_changes"
  experienceData: {
    title: string
    description: string
    host: {
      name: string
      email: string
    }
    pricing: {
      basePrice: number
      currency: string
    }
    images: string[]
  }
  submittedAt: any
  validationErrors: string[]
  validationWarnings: string[]
  formalooSubmissionId: string
}

export default function ExperienceSubmissionsPage() {
  const [submissions, setSubmissions] = useState<PendingSubmission[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedSubmission, setSelectedSubmission] = useState<PendingSubmission | null>(null)
  const [reviewModalOpen, setReviewModalOpen] = useState(false)
  const [reviewAction, setReviewAction] = useState<"approve" | "reject" | "changes" | null>(null)
  const [reviewNotes, setReviewNotes] = useState("")
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    loadSubmissions()
  }, [])

  const loadSubmissions = async () => {
    try {
      // TODO: Implement API call to fetch pending submissions
      // const response = await fetch('/api/admin/experience-submissions')
      // const data = await response.json()
      // setSubmissions(data.submissions)
      
      // Mock data for now
      setSubmissions([
        {
          id: "sub1",
          status: "pending",
          experienceData: {
            title: "River Rafting Adventure",
            description: "Exciting river rafting experience...",
            host: {
              name: "Cross City Tours Jamaica",
              email: "<EMAIL>"
            },
            pricing: {
              basePrice: 99,
              currency: "USD"
            },
            images: ["https://example.com/image1.jpg"]
          },
          submittedAt: new Date(),
          validationErrors: ["Missing city field", "Missing duration field"],
          validationWarnings: ["No host bio provided"],
          formalooSubmissionId: "Zfa2huHyjr9Dy3BSDCnF"
        }
      ])
    } catch (error) {
      console.error("Error loading submissions:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleReviewSubmission = (submission: PendingSubmission, action: "approve" | "reject" | "changes") => {
    setSelectedSubmission(submission)
    setReviewAction(action)
    setReviewNotes("")
    setReviewModalOpen(true)
  }

  const submitReview = async () => {
    if (!selectedSubmission || !reviewAction) return

    setProcessing(true)
    try {
      // TODO: Implement API call to process review
      const response = await fetch('/api/admin/experience-submissions/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_ADMIN_API_KEY}`
        },
        body: JSON.stringify({
          submissionId: selectedSubmission.id,
          action: reviewAction,
          notes: reviewNotes
        })
      })

      if (response.ok) {
        // Refresh submissions
        await loadSubmissions()
        setReviewModalOpen(false)
        setSelectedSubmission(null)
      } else {
        console.error("Failed to process review")
      }
    } catch (error) {
      console.error("Error processing review:", error)
    } finally {
      setProcessing(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="text-yellow-600"><Clock className="w-3 h-3 mr-1" />Pending</Badge>
      case "approved":
        return <Badge variant="outline" className="text-green-600"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>
      case "rejected":
        return <Badge variant="outline" className="text-red-600"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>
      case "needs_changes":
        return <Badge variant="outline" className="text-orange-600"><AlertCircle className="w-3 h-3 mr-1" />Needs Changes</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (date: any) => {
    return new Date(date).toLocaleDateString()
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading submissions...</p>
        </div>
      </div>
    )
  }

  const pendingSubmissions = submissions.filter(s => s.status === "pending")
  const reviewedSubmissions = submissions.filter(s => s.status !== "pending")

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Experience Submissions</h1>
        <p className="mt-2 text-muted-foreground">Review and approve experience submissions from Formaloo</p>
      </div>

      <Tabs defaultValue="pending" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pending">
            Pending ({pendingSubmissions.length})
          </TabsTrigger>
          <TabsTrigger value="reviewed">
            Reviewed ({reviewedSubmissions.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          {pendingSubmissions.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No pending submissions</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {pendingSubmissions.map((submission) => (
                <Card key={submission.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{submission.experienceData.title}</CardTitle>
                        <CardDescription>
                          By {submission.experienceData.host.name} • {formatDate(submission.submittedAt)}
                        </CardDescription>
                      </div>
                      {getStatusBadge(submission.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <strong>Price:</strong> {formatPrice(submission.experienceData.pricing.basePrice, submission.experienceData.pricing.currency)}
                      </div>
                      <div>
                        <strong>Host Email:</strong> {submission.experienceData.host.email}
                      </div>
                      <div>
                        <strong>Images:</strong> {submission.experienceData.images.length} uploaded
                      </div>
                    </div>

                    {submission.validationErrors.length > 0 && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm font-medium text-red-800 mb-1">Validation Errors:</p>
                        <ul className="text-sm text-red-700 list-disc list-inside">
                          {submission.validationErrors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {submission.validationWarnings.length > 0 && (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <p className="text-sm font-medium text-yellow-800 mb-1">Warnings:</p>
                        <ul className="text-sm text-yellow-700 list-disc list-inside">
                          {submission.validationWarnings.map((warning, index) => (
                            <li key={index}>{warning}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedSubmission(submission)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View Details
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleReviewSubmission(submission, "approve")}
                        disabled={submission.validationErrors.length > 0}
                      >
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Approve
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewSubmission(submission, "changes")}
                      >
                        <AlertCircle className="w-4 h-4 mr-1" />
                        Request Changes
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleReviewSubmission(submission, "reject")}
                      >
                        <XCircle className="w-4 h-4 mr-1" />
                        Reject
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="reviewed" className="space-y-4">
          {reviewedSubmissions.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No reviewed submissions</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {reviewedSubmissions.map((submission) => (
                <Card key={submission.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{submission.experienceData.title}</CardTitle>
                        <CardDescription>
                          By {submission.experienceData.host.name} • {formatDate(submission.submittedAt)}
                        </CardDescription>
                      </div>
                      {getStatusBadge(submission.status)}
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Review Modal */}
      <Dialog open={reviewModalOpen} onOpenChange={setReviewModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {reviewAction === "approve" && "Approve Experience"}
              {reviewAction === "reject" && "Reject Experience"}
              {reviewAction === "changes" && "Request Changes"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="notes">Notes (optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any notes or feedback..."
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setReviewModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={submitReview} disabled={processing}>
                {processing ? "Processing..." : "Confirm"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
